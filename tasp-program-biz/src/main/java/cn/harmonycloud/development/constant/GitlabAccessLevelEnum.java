package cn.harmonycloud.development.constant;

/**
 * GitLab权限级别枚举
 */
public enum GitlabAccessLevelEnum {
    NO_ACCESS(0, "无权限"),
    MINIMAL(5, "最小权限"),
    GUEST(10, "访客"),
    REPORTER(20, "报告者"),
    DEVELOPER(30, "开发者"),
    MAINTAINER(40, "维护者"),
    OWNER(50, "所有者");

    private final int level;
    private final String description;

    GitlabAccessLevelEnum(int level, String description) {
        this.level = level;
        this.description = description;
    }

    public int getLevel() {
        return level;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据权限级别获取枚举
     */
    public static GitlabAccessLevelEnum fromLevel(int level) {
        for (GitlabAccessLevelEnum accessLevel : values()) {
            if (accessLevel.level == level) {
                return accessLevel;
            }
        }
        throw new IllegalArgumentException("无效的GitLab权限级别: " + level);
    }
}