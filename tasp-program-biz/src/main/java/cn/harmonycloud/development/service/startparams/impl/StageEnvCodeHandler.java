package cn.harmonycloud.development.service.startparams.impl;

import cn.harmonycloud.development.outbound.api.dto.pipeline.JenkinsFileStartParameter;
import cn.harmonycloud.development.service.startparams.RunStartParamsContext;
import cn.harmonycloud.development.service.startparams.RunStartParamsEnum;
import cn.harmonycloud.development.service.startparams.RunStartParamsHandler;
import org.springframework.stereotype.Component;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/8/7 2:42 下午
 **/
@Component
public class StageEnvCodeHandler implements RunStartParamsHandler {

    @Override
    public RunStartParamsEnum getParams() {
        return RunStartParamsEnum.STAGE_ENV_CODE;
    }

    @Override
    public void setValue(JenkinsFileStartParameter startParameter, RunStartParamsContext context) {
        String stageEnvCode = context.getDevopsStageEnvDto().getStageEnvCode();
        if (stageEnvCode == null ) stageEnvCode = "";
        startParameter.setValue(stageEnvCode);
    }
    
}
