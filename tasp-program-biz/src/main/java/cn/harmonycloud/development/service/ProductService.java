package cn.harmonycloud.development.service;

import cn.harmonycloud.development.outbound.api.dto.promotion.SavePromotionRelationReq;
import cn.harmonycloud.development.pojo.dto.product.DevopsProductMetadataDto;
import cn.harmonycloud.development.pojo.dto.product.ProductTreeDto;
import cn.harmonycloud.development.pojo.vo.repository.ProductTreeQuery;
import cn.harmonycloud.development.pojo.vo.repository.ProductVersionQuery;

import java.util.List;

public interface ProductService {

    /**
     * 查询制品树形结构列表
     *
     * @param request
     * @return
     */
    List<ProductTreeDto> tree(ProductTreeQuery request);

    /**
     * 制品的版本列表
     *
     * @param query
     * @return
     */
    List<String> versionList(ProductVersionQuery query);


    void savaPromotionRelation(SavePromotionRelationReq savePromotionRelationReq);

    /**
     * 查询版本下的所有制品
     *
     * @param subsystemId
     * @param versionNumber
     * @param repoId
     * @param format
     */
    List<DevopsProductMetadataDto> listByVersion(Long subsystemId, String versionNumber, Long repoId, String format);
}
