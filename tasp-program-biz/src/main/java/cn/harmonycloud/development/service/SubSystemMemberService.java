package cn.harmonycloud.development.service;

import cn.harmonycloud.development.pojo.query.MemberQuery;
import cn.harmonycloud.development.pojo.vo.system.*;
import cn.harmonycloud.development.outbound.api.dto.role.RoleInfoDto;
import cn.harmonycloud.pmp.model.vo.UserVo;
import cn.harmonycloud.pojo.subsystem.SubsystemDto;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * 
 * 子系统成员管理
 */
public interface SubSystemMemberService {


    /**
     * 查询子系统成员列表
     *
     * @return
     */
    List<RoleInfoDto> roles();

    /**
     * 新增子系统成员
     *
     * @param req
     * @param syncGitlab
     */
    void createMember(CreateMemberVO req, Boolean syncGitlab);

    /**
     * 删除子系统成员
     *
     * @param deleteMemberVO
     */
    void deleteMember(DeleteMemberVO deleteMemberVO);

    /**
     * 查询子系统成员列表（分页）
     *
     * @param req
     * @return
     */
    Page<ListSystemMemberVO> querySystemMember(MemberQuery req);

    /**
     * 查询子系统成员列表
     *
     * @param
     * @return
     */
    List<UserVo> listOverUser(Long subSystemId, String queryParam);

    /**
     * 查询子系统信息
     *
     * @return
     */
    List<ListSubSystemByCurrentVo> listByCurrent();

    /**
     * 查询子系统所有成员
     *
     * @param subSystemId
     * @param user
     * @return
     */
    List<ListSystemMemberVO> allMember(Long subSystemId, String user);

    /**
     * 子系统管理员角色
     *
     * @return
     */
    RoleInfoDto subSystemAdminRoles();

    /**
     * 初始化代码库成员
     *
     * @param subSystemId
     * @param gitlabId
     */
    void initGitlabMember(Long subSystemId, Long gitlabId);

    /**
     * 查询子系统成员加上所在系统管理员
     * @return
     */
    List<UserVo> listMember(Long subSystemId, Boolean withSystemAdmin);

    /**
     * 保存用户角色（删除原有角色）
     *
     * @param memberDto
     */
    void saveMember(SaveMemberDto memberDto);

    /**
     * 更新子系统成员
     *
     * @param req
     */
    void modifyMember(ModifyMemberVo req);

    /**
     * 查询用户在子系统下的角色信息
     *
     * @param systemId 系统id
     * @param userId 用户id
     * @return
     */
    List<SubsystemDto> listSubsystemRole(Long systemId, Long userId);

    /**
     * 批量更新角色信息
     *
     * @return
     */
    void modifyMemberBatch(ModifyMemberBatchRequest req);
}
