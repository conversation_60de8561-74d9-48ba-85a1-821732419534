package cn.harmonycloud.development.service;

import cn.harmonycloud.development.pojo.vo.gallery.PipelineViewUpdateRequest;
import cn.harmonycloud.development.pojo.vo.pipeline.*;
import cn.harmonycloud.development.outbound.api.dto.pipeline.BuildDetailDto;
import cn.harmonycloud.development.outbound.api.dto.pipeline.JobDto;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;

public interface PipelineService {

    /**
     * 获取子系统下所有流水线
     *
     * @param subSystemId
     * @param envCode
     * @return
     */
    List<PipelineVo> getPipelineBySystemId(Long subSystemId, String envCode, String draftStatus);

    /**
     * 创建流水线
     *
     * @param request
     * @return
     */
    Long createPipeline(PipelineCreateRequest request);

    /**
     * 流水线分页列表
     *
     * @param request
     * @return
     */
    Page<JobDto> listByPage(PipelinePageRequest request);

    /**
     * 更新流水线在子系统的展示状态
     *
     * @param request
     */
    void pipelineViewUpdate(PipelineViewUpdateRequest request);

    /**
     * 查询概览的流水线
     *
     * @param subSystemId
     * @return
     */
    List<BuildDetailDto> pipelineView(Long subSystemId);

    /**
     * 创建子系统和流水线的绑定关系
     *
     * @param request
     */
    void createPipelineBinding(PipelineBindingRequest request);

    /**
     * 批量关联流水线
     *
     * @param request
     */
    void relevanceJobs(RelevanceJobsRequest request);

    /**
     * 取消关联流水线
     *
     * @param request
     */
    void unBind(PipelineBindingRequest request);

    /**
     * 取消关联流水线(删除系统或子系统)
     *
     * @param subsystemIds
     */
    void unBind(List<Long> subsystemIds);
}
