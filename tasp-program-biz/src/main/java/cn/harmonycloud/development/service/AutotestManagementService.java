package cn.harmonycloud.development.service;

import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.development.pojo.dto.autotest.AutotestResultDTO;
import cn.harmonycloud.development.pojo.dto.autotest.AutotestDTO;
import cn.harmonycloud.development.pojo.dto.autotest.CreateAutotestDTO;
import cn.harmonycloud.development.pojo.vo.autotest.AutotestDetailResultListVO;
import cn.harmonycloud.development.pojo.vo.autotest.AutotestDetailVO;
import cn.harmonycloud.development.pojo.vo.autotest.AutotestVO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;

public interface AutotestManagementService {

    /**
     * 自动化测试创建
     * @param dto
     */
    void create(CreateAutotestDTO dto);
    /**
     * 开启自动化测试
     * @param testIdList 自动化测试id
     */
    List<AutotestVO> startAutotestList(List<Long> testIdList);

    /**
     * 自动化测试结果
     * @param dto 自动化测试id
     */
    BaseResult result(AutotestResultDTO dto);

    /**
     * 自动化测试详情
     * @param id 自动化测试详情
     */
    AutotestDetailVO autotestDetailList(Long id);

    /**
     * 自动化测试详情列表
     * @param id 自动化测试详情
     */
    List<AutotestDetailResultListVO> autotestDetailResultList(Long id);

    /**
     * 自动化测试分页列表
     * @param dto
     * @return
     */
    Page<AutotestVO> getPage(AutotestDTO dto);

    /**
     * 自动化测试状态变更
     * @param id
     * @param status
     */
    Boolean updateStatus(Long id, Integer status);
}
