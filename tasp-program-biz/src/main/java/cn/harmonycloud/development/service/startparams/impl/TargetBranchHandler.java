package cn.harmonycloud.development.service.startparams.impl;

import cn.harmonycloud.development.outbound.SystemDictRepository;
import cn.harmonycloud.development.outbound.api.dto.pipeline.JenkinsFileStartParameter;
import cn.harmonycloud.development.pojo.dto.devopsstage.DevopsStageEnvDto;
import cn.harmonycloud.development.pojo.entity.SystemDict;
import cn.harmonycloud.development.service.startparams.RunStartParamsContext;
import cn.harmonycloud.development.service.startparams.RunStartParamsEnum;
import cn.harmonycloud.development.service.startparams.RunStartParamsHandler;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.Serializable;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/8/7 2:22 下午
 **/
@Component
public class TargetBranchHandler implements RunStartParamsHandler {

    @Autowired
    private SystemDictRepository systemDictRepository;

    @Override
    public RunStartParamsEnum getParams() {
        return RunStartParamsEnum.TARGET_BRANCH;
    }

    @Override
    public void setValue(JenkinsFileStartParameter startParameter, RunStartParamsContext context) {
        DevopsStageEnvDto devopsStageEnvDto = context.getDevopsStageEnvDto();
        SystemDict systemDict = systemDictRepository.getCacheById(devopsStageEnvDto.getStageDictId());
        String targetBranch = systemDict.getDictCode() + "_" + devopsStageEnvDto.getId();
        if(StringUtils.isNotEmpty(devopsStageEnvDto.getStageEnvCode())){
            // 2.2版本以后的数据才有stageEnvCode编码
            targetBranch = systemDict.getDictCode() + "_" + devopsStageEnvDto.getStageEnvCode();
        }
        startParameter.setValue(targetBranch);
    }
}
