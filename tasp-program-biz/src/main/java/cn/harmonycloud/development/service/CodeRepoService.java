package cn.harmonycloud.development.service;

import cn.harmonycloud.development.outbound.api.dto.coderepo.BranchDto;
import cn.harmonycloud.development.outbound.api.dto.coderepo.BranchStageDto;
import cn.harmonycloud.development.outbound.api.dto.coderepo.TagDto;
import cn.harmonycloud.development.pojo.entity.DevopsSubSystem;
import cn.harmonycloud.development.pojo.vo.testenv.SubmitMergeSourceBranchRequest;
import cn.harmonycloud.development.outbound.api.dto.scm.GitProjectDto;

import java.util.List;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/8/22 5:37 下午
 **/
public interface CodeRepoService {

    /**
     * 根据子系统id查询gitlabid （不存在抛出异常）
     *
     * @param subSystemId 子系统id
     * @return
     */
    Integer getScmIdBySystemId(Long subSystemId);

    /**
     * 根据子系统id查询gitlabid （不存在返回空）
     *
     * @param subSystemId 子系统id
     * @return
     */
    Integer getCodeRepoIdBySystemId(Long subSystemId);

    /**
     * 根据子系统id查询gitlabid （不存在返回空）
     *
     * @param subSystemId 子系统id
     * @return key 子系统id   v-代码库id
     */
    Map<Long, Integer> mapCodeRepoIdBySystemId(List<Long> subSystemId);

    /**
     * 保存合并请求
     *
     * @param subSystemId 子系统id
     * @param envCode 环境编码
     * @param mergeId 合并id
     * @param branches 分支列表
     */
    void saveMergeTask(Long subSystemId, String envCode, Long mergeId, List<SubmitMergeSourceBranchRequest> branches);

    /**
     * 获取全局配置的基础分支
     *
     * @return
     */
    String getBaseBranch();

    /**
     * 根据子系统gid查询项目信息
     *
     * @param subSystemId 子系统id
     * @return
     */
    GitProjectDto getProject(Long subSystemId, boolean throwException);

    /**
     * 根据子系统id查询关联代码库的分支
     *
     * @param subSystemId 子系统id
     * @return
     */
    List<BranchDto> branches(Long subSystemId);

    /**
     * 统计代码合并数
     *
     * @param subsystemId 子系统id
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @return
     */
    Integer merCount(Long subsystemId, String startTime, String endTime);

    /**
     * 初始化代码扫描
     *
     * @param subsystemId 子系统id
     * @param technology 语言类型
     */
    void initCodeScan(Long subsystemId, String technology);

    /**
     * 查询分支差异
     *
     * @param subsystemId 子系统gid
     * @param branch      分支名称
     * @return
     */
    BranchStageDto branchStage(Long subsystemId, String branch);

    /**
     *
     *
     * @param subSystemId 子系统id
     * @return
     */
    String baseBranch(Long subSystemId);

    /**
     * tag列表
     *
     * @param subSystemId 子系统id
     * @return
     */
    List<TagDto> tagList(Long subSystemId, String name);

    /**
     * 更新代码库名称基本信息
     *
     * @param devopsSubSystem 子系统
     */
    void updateBySubsystem(DevopsSubSystem devopsSubSystem);

    /**
     * 根据子系统查询分支以及标签列表
     *
     * @param subSystemId
     * @return
     */
    List<String> listBranchAndTag(Long subSystemId);

    /**
     * 清理代码库下的临时分支
     *
     * @param codeRepoId 代码库id
     * @param clearTemporaryBranchTime 清理时间：单位 天。表示清理多少前天创建的临时分支
     */
    void clearMergeBranch(Integer codeRepoId, Integer clearTemporaryBranchTime);
}
