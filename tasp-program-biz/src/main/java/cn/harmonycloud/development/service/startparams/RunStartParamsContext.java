package cn.harmonycloud.development.service.startparams;

import cn.harmonycloud.development.outbound.api.dto.pipeline.JenkinsFileStartParameter;
import cn.harmonycloud.development.pojo.dto.devopsstage.DevopsStageEnvDto;
import cn.harmonycloud.development.pojo.entity.DevopsSubSystem;
import cn.harmonycloud.development.pojo.entity.VersionManagement;
import lombok.Data;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/8/4 5:15 下午
 **/
@Data
public class RunStartParamsContext {

    private DevopsSubSystem devopsSubSystem;

    private DevopsStageEnvDto devopsStageEnvDto;

    private List<JenkinsFileStartParameter> runStartParams;

    private VersionManagement versionManagement;

    public RunStartParamsContext(VersionManagement versionManagement){
        this.versionManagement = versionManagement;
    }


    public RunStartParamsContext(DevopsSubSystem devopsSubSystem, DevopsStageEnvDto devopsStageEnvDto, List<JenkinsFileStartParameter> runStartParams){
        this.devopsSubSystem = devopsSubSystem;
        this.devopsStageEnvDto = devopsStageEnvDto;
        this.runStartParams = runStartParams;
    }

}
