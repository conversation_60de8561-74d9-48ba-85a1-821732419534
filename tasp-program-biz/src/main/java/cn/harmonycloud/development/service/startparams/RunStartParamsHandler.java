package cn.harmonycloud.development.service.startparams;

import cn.harmonycloud.development.outbound.api.dto.pipeline.JenkinsFileStartParameter;

/**
 * @Description 启动变量处理器
 * <AUTHOR>
 * @Date 2023/8/4 3:22 下午
 **/
public interface  RunStartParamsHandler {

    /**
     * 获取启动参数名称
     *
     * @return
     */
    RunStartParamsEnum getParams();

    /**
     * 添加启动参数的条件
     *
     * @param context
     * @return
     */
    default Boolean condition(RunStartParamsContext context){
        return Boolean.TRUE;
    }

    /**
     * 获取默认的启动参数对象
     *
     * @return
     */
    default JenkinsFileStartParameter getDefaultModel(){
        return new JenkinsFileStartParameter(getParams().getParamsName(), "");
    }

    /**
     * 设置默认值
     *
     * @param startParameter
     * @param context
     */
    default void setValue(JenkinsFileStartParameter startParameter, RunStartParamsContext context){

    }

    default void setRunValue(JenkinsFileStartParameter startParameter, RunStartParamsContext context){

    }
}
