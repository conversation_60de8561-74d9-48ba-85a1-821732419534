package cn.harmonycloud.development.service;

import cn.harmonycloud.development.outbound.api.dto.project.IssuesDto;
import cn.harmonycloud.development.pojo.dto.project.*;
import cn.harmonycloud.development.pojo.vo.project.*;

import java.util.List;
import java.util.Map;

public interface ProjectManagementService {

    /**
     * 根据特性id列表查询所关联的需求列表
     *
     * @param featureId
     * @return
     */
  //  List<IssuesDto> listIssues(List<Long> featureId);

    /**
     * 根据特性id列表查询所关联的需求列表
     *
     * @param featureId
     * @return key: featureId
     * @return value: 需求列表
     */
    Map<Long, List<IssuesDto>> mapIssues(List<Long> featureId);

    /**
     * 根据特性id查询需求列表，携带项目信息
     *
     * @param featureId
     * @return
     */
    List<IssuesWithProjectDto> listIssuesWithProjectName(List<Long> featureId);

    /**
     * 根据特性id查询项目列表
     *
     * @param featureId
     * @return
     */
    List<ProjectDto> groupProjectIssues(List<Long> featureId);

    /**
     *
     * @param projectId 项目id
     * @param queryType 0-全部  1-我负责的
     * @param issuesClassicIdList 事项类型：1-需求；2-任务；3-缺陷
     * @return
     */
 //   List<IssuesDto> taskList(Long projectId, Integer queryType, List<Long> issuesClassicIdList);

    /**
     * 跟踪事项通用数据
     *
     * @return
     */
 //   List<IssuesDTO> getCustomIssuesByFeatureIds(List<Long> featureId);

}
