package cn.harmonycloud.development.service.startparams.impl;

import cn.harmonycloud.development.outbound.api.dto.pipeline.JenkinsFileStartParameter;
import cn.harmonycloud.development.service.startparams.RunStartParamsContext;
import cn.harmonycloud.development.service.startparams.RunStartParamsEnum;
import cn.harmonycloud.development.service.startparams.RunStartParamsHandler;
import org.springframework.stereotype.Component;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/8/7 3:24 下午
 **/
@Component
public class DeployEnvHandler implements RunStartParamsHandler {


    @Override
    public RunStartParamsEnum getParams() {
        return RunStartParamsEnum.DEPLOY_ENV;
    }

    @Override
    public void setValue(JenkinsFileStartParameter startParameter, RunStartParamsContext context) {
        startParameter.setValue(context.getDevopsStageEnvDto().getEnvId().toString());
    }
}
