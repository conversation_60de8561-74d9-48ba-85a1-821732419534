package cn.harmonycloud.development.service.startparams.impl;

import cn.harmonycloud.constants.DevelopmentConstance;
import cn.harmonycloud.development.outbound.api.dto.pipeline.JenkinsFileStartParameter;
import cn.harmonycloud.development.pojo.dto.devopsstage.DevopsStageEnvDto;
import cn.harmonycloud.development.service.startparams.RunStartParamsContext;
import cn.harmonycloud.development.service.startparams.RunStartParamsEnum;
import cn.harmonycloud.development.service.startparams.RunStartParamsHandler;
import org.springframework.stereotype.Component;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/8/7 3:29 下午
 **/
@Component
public class NamespaceHandler implements RunStartParamsHandler {



    @Override
    public RunStartParamsEnum getParams() {
        return RunStartParamsEnum.NAMESPACE;
    }

    @Override
    public void setValue(JenkinsFileStartParameter startParameter, RunStartParamsContext context) {
        DevopsStageEnvDto devopsStageEnvDto = context.getDevopsStageEnvDto();
        if(devopsStageEnvDto.getDeployType() == DevelopmentConstance.EnvDeployType.k8s){
            startParameter.setValue(devopsStageEnvDto.getNamespace());
            startParameter.setUpdateFlag(Boolean.FALSE);
        }
    }
}
