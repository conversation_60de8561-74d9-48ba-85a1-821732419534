package cn.harmonycloud.development.service;

import cn.harmonycloud.development.pojo.dto.web.WebFile;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletResponse;
import java.util.List;

public interface SystemFileService {

    WebFile getWebFileById(Long id);

    List<WebFile> getWebFileByIds(List<Long> id);

    WebFile upload(MultipartFile files, String subject);

    void download(Long id, HttpServletResponse response);
}
