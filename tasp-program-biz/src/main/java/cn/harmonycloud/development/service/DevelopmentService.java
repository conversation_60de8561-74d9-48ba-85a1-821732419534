package cn.harmonycloud.development.service;


import cn.harmonycloud.development.pojo.dto.development.*;
import cn.harmonycloud.development.pojo.dto.feature.FeatureTaskDTO;
import cn.harmonycloud.development.pojo.vo.pipeline.PipelineMergeNotify;
import cn.harmonycloud.development.pojo.vo.pipeline.PipelineNotifyReq;
import cn.harmonycloud.development.pojo.vo.pipeline.PipelineProductBuildReq;
import cn.harmonycloud.development.pojo.vo.project.ProjectDto;
import cn.harmonycloud.development.outbound.api.dto.pipeline.BuildDetailDto;
import cn.harmonycloud.development.outbound.api.dto.pipeline.JenkinsFileStartParameter;
import cn.harmonycloud.pojo.version.VersionDto;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;
import java.util.Map;

public interface DevelopmentService {

    /**
     * 可选列表
     * @param requestVo
     */
    List<FeatureTaskDTO> featureQuery(QueryFeatureRequest requestVo);

    /**
     * 集成特性列表（阶段）
     * @param stageEnvId 阶段环境id
     * @param versionId 版本id
     * @return
     */
    List<FeatureTaskDTO> featureIntegration(Long stageEnvId, Long versionId);

    /**
     * 阶段流水线构建
     * @param request
     */
    void pipelineBuild(PipelineBuildRequest request);

    /**
     * 发起检修
     * @param request
     */
    @Deprecated // 功能已删除
    OnlineDetails online(OnlineRequest request);

    /**
     * 特性列表分页 携带分支字段、携带关联任务字段
     * @param requestVo
     * @return
     */
    Page<FeatureTaskDTO> featurePage(QueryFeatureRequest requestVo);

    /**
     * 添加特性到集成环境中
     * @param request
     */
    void addIntegration(StageEnvFeatureRequest request);

    /**
     * 移除环境中的特性列表
     * @param request
     */
    void removeIntegration(StageEnvFeatureRequest request);

    /**
     * 检修单分页列表
     * @param request
     */
    @Deprecated // 功能已删除
    Page<OnlineDto> onlinePage(OnlineQuery request);

    /**
     * 保存检修单
     * @param request
     */
    @Deprecated // 功能已删除
    void onlineSave(OnlineCreateRequest request);

    /**
     * 检修单详情
     * @param orderId 检修单id
     * @return
     */
    @Deprecated // 功能已删除
    OnlineDetails onlineDetails(Long orderId);

    /**
     * 检修单列表
     * @param buildInstanceId 构建实例id
     * @return
     */
    @Deprecated // 功能已删除
    List<ProjectDto> onlineIssues(Long buildInstanceId);
    /**
     * 流水线冲突展示接口
     * @param buildId 构建id
     * @return
     */
    ConflictPlanDto conflictPlan(Long buildId);

    /**
     * 撤销提测单
     * @param id 提测单id
     * @return
     */
    void onlineCancel(Long id);

    /**
     * 删除提测单
     * @param id 提测单id
     * @return
     */
    void onlineDel(Long id);

    /**
     * 获取流水线构建的变量
     *
     * @param jobId
     * @param buildId
     * @return
     */
    Map getPipelineVar(Long jobId, Long buildId);

    /**
     * 研发工作台查询构建详情
     *
     * @param stageEnvId
     * @param jobId
     * @param versionId
     * @return
     */
    BuildDetailDto pipelineBuildInfo(Long stageEnvId, Long jobId, Long versionId);

    /**
     * 流水线构建结果通知
     *
     * @param buildId
     * @param notifyReq
     */
    void notifyResult(Long buildId, PipelineNotifyReq notifyReq);

    /**
     * 流水线分支合并通知
     * @param notify
     */
    void mergeNotify(PipelineMergeNotify notify);

    /**
     * 流水线启动参数列表
     *
     * @param jobId 任务id
     * @return
     */
    List<JenkinsFileStartParameter> runStartParams(Long jobId, Long stageEnvId, Long marjoVersionId, Boolean lastFlag);

    /**
     * 流水线重试
     *
     * @param request
     */
    void pipelineReplay(PipelineReplayRequest request);

    /**
     * 查询最近一次信息
     *
     * @param stageEnvId
     * @param versionId
     * @return
     */
    BuildInstanceDto lastBuildInstance(Long stageEnvId, Long versionId);

    /**
     * 晋级版本校验
     *
     * @param req
     */
    VersionDto checkSubversion(PipelineProductBuildReq req);
}
