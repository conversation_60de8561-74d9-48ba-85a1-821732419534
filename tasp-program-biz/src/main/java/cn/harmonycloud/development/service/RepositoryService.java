package cn.harmonycloud.development.service;

import cn.harmonycloud.development.outbound.api.dto.repository.DevopsRepository;
import cn.harmonycloud.development.outbound.api.dto.pipeline.DoDeployEnv;
import cn.harmonycloud.development.pojo.vo.repository.CreateRepositoryRequest;

import java.util.List;

public interface RepositoryService {
    /**
     * 新建制品仓库
     *
     * @param request
     */
    void createRepository(CreateRepositoryRequest request);

    /**
     * 制品库列表
     *
     *
     * @param configId
     * @param systemId 系统id
     * @param subsystemId 子系统id
     * @param repoName 制品库名称
     * @return
     */
    List<DevopsRepository> list(Long configId, Long systemId, Long subsystemId, String format, String repoName, Boolean withPublicRepository, Boolean withProdRepository);

    /**
     * 仓库id
     *
     * @param id
     */
    void remove(Long id);

    /**
     * 环境列表下拉框
     *
     * @param systemId
     * @return
     */
    List<DoDeployEnv> envList(Long systemId);

    /**
     * 目标仓库列表
     *
     * @param systemId
     * @param format
     * @param sourceRepoId
     * @return
     */
    List<DevopsRepository> targetRepository(Long systemId, String format, Long sourceRepoId);
}
