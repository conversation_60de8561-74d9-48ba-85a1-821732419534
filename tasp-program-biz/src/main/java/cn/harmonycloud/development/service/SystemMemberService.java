package cn.harmonycloud.development.service;
import cn.harmonycloud.development.pojo.query.MemberQuery;
import cn.harmonycloud.development.pojo.vo.system.*;
import cn.harmonycloud.development.outbound.api.dto.role.RoleInfoDto;
import cn.harmonycloud.pmp.model.vo.UserVo;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【system_member(成员表)】的数据库操作Service
* @createDate 2022-08-02 11:14:36
*/
public interface SystemMemberService{

    /**
     * 获取系统角色列表
     * @return
     */
    List<RoleInfoDto> systemRoles();
    /**
     * 获取系统资源-管理员角色信息
     * @return
     */
    RoleInfoDto systemAdminRoles();
    /**
     * 添加系统成员
     * @param createMemberVO
     */
    void createMember(CreateMemberVO createMemberVO);

    /**
     * 添加gitlab成员
     *
     * @param createMemberVO
     */
    void addGitlabMember(CreateMemberVO createMemberVO);

    /**
     * 添加系统成员
     * @param createMemberVO
     */
    void saveMember(SaveMemberDto createMemberVO);

    /**
     * 删除系统成员
     * @param deleteMemberVO
     */
    void deleteMember(DeleteMemberVO deleteMemberVO);

    /**
     * 查询系统成员
     * @param query
     * @return
     */
    Page<ListSystemMemberVO> querySystemMember(MemberQuery query);

    /**
     * 查询租户下所有用户、排除已经添加到系统的人员
     * @param systemId
     * @return
     */
    List<UserVo> listOverUser(Long systemId, String queryParam);


    List<ListSystemByCurrentVo> listByCurrent(ListSystemByCurrentRequest request);

    /**
     * 更新系统成员
     *
     * @param req
     */
    void modifyMember(ModifyMemberVo req);
}
