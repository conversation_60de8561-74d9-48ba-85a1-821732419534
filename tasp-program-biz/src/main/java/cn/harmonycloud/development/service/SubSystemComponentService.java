package cn.harmonycloud.development.service;

import cn.harmonycloud.development.pojo.dto.scm.RelateProjectRequest;
import cn.harmonycloud.development.pojo.vo.config.SubConfigCreateRspVO;
import cn.harmonycloud.development.pojo.vo.config.SubConfigCreateVO;
import cn.harmonycloud.development.pojo.vo.scm.MyMergeRequestVO;
import cn.harmonycloud.development.pojo.vo.scm.ProjectListVO;
import cn.harmonycloud.development.outbound.api.dto.scm.GitProjectDto;
import cn.harmonycloud.development.outbound.api.dto.scm.ProjectRequest;
import cn.harmonycloud.development.outbound.api.dto.scm.UpdateProjectRequest;

import java.util.List;

public interface SubSystemComponentService {

    /**
     * 子系统所关联的代码仓库信息
     *
     * @param subSystemId
     * @return
     */
    GitProjectDto gitlabInfo(Long subSystemId);

    /**
     * 子系统的合并请求
     *
     * @param subSystemId
     * @param type
     * @return
     */
    List<MyMergeRequestVO> myMergeRequest(Long subSystemId, Integer type);

    /**
     * 更新子系统关联关系
     *
     * @param oldId
     * @param newId
     * @return
     */
    GitProjectDto updateRelation(String oldId, String newId);

    /**
     * 删除代码库关联
     *
     * @param gitlabId
     */
    void deleteRelation(String gitlabId);

    /**
     * 新建子系统代码仓库
     *
     * @param subSystemId
     * @param request
     */
    Long createProject(Long subSystemId, ProjectRequest request, boolean initCodeScan, boolean initMember);

    /**
     * 关联子系统代码仓库
     *
     * @param request
     * @return
     */
    GitProjectDto relateProject(RelateProjectRequest request, boolean initCodeScan, boolean initMember);

    /**
     * 查询子系统代码仓库列表
     *
     * @param subSystemId
     * @return
     */
    List<GitProjectDto> getProjectDetail(String subSystemId);

    /**
     * 查询子系统代码库列表
     *
     * @param systemId
     * @param search
     * @return
     */
    List<ProjectListVO> getProjectList(String systemId, String search);

    /**
     * 更新代码库信息
     *
     * @param request
     */
    void updateProject(UpdateProjectRequest request);

    /**
     * 创建或关联代码仓库
     * @param subSystemId
     * @param gitlabId 代码仓库id null或者0时表示新建，其他表示关联
     * @return
     */
    Integer createProject(Long subSystemId, Integer gitlabId);

    /**
     * 查询构建应用配置库
     *
     * @param createVO
     * @return
     */
    SubConfigCreateRspVO createConfig(SubConfigCreateVO createVO);
}
