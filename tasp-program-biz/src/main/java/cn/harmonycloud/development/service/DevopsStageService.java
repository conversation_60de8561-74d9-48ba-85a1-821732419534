package cn.harmonycloud.development.service;

import cn.harmonycloud.development.pojo.dto.devopsstage.*;
import cn.harmonycloud.development.pojo.entity.DevopsStageEnv;
import cn.harmonycloud.pojo.devopsstage.DevopsStageEnvRspDto;

import java.util.List;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/2/7 10:14 上午
 **/
public interface DevopsStageService {

    /**
     * 子系统下阶段列表（只展示已配置的阶段）
     *
     * @param subSystemId 子系统id
     * @return
     */
    List<DevopsStageDto> list(Long subSystemId);

    /**
     * 阶段详情
     *
     * @param stageId 阶段id
     * @return
     */
    DevopsStageDto info(Long stageId);

    /**
     * 阶段详情
     *
     * @param stageEnvId 阶段环境id
     * @return
     */
    DevopsStageDto infoByStageEnvId(Long stageEnvId);

    /**
     * 阶段保存
     *
     * @param request
     */
    void configSave(DevopsStageSaveRequest request);

    /**
     * 子系统阶段下的环境列表
     *
     * @param request
     * @return
     */
    List<DevopsStageEnvDto> envList(DevopsStageEnvListRequest request);

    /**
     * 阶段环境列表
     *
     * @param stageEnvIds 阶段环境id列表
     * @return
     */
    List<DevopsStageEnvDto> envList(List<Long> stageEnvIds);

    /**
     * 查询阶段环境详情
     *
     * @param stageEnvId 阶段环境id
     * @return
     */
    DevopsStageEnvDto envInfo(Long stageEnvId);

    /**
     * 查询阶段环境详情
     *
     * @param stageEnvId 阶段环境id
     * @return
     */
    DevopsStageEnvDto envInfo(Long stageEnvId, boolean details);


    /**
     * 创建一个环境
     *
     * @param request
     */
    DevopsStageEnv envCreate(DevopsStageEnvCreate request);


    /**
     * 编辑环境
     *
     * @param request
     * @return
     */
    void envModify(DevopsStageEnvModify request);

    /**
     * 环境删除（逻辑删除）
     *
     * @param stageEnvId
     */
    void envDelete(Long stageEnvId);

    /**
     * 获取所有系统配置阶段
     *
     * @param
     * @return
     */
    Map<Long, SystemStage> mapAllSystemStage();

    /**
     * 获取所有系统配置阶段
     *
     * @param
     * @return
     */
    SystemStage getSystemStageByEnvId(Long stageEnvId);

    /**
     * 查询已使用的环境列表
     *
     *
     * @param systemId
     * @param deployEnv
     * @param deployIds
     * @return
     */
    Map<Integer, List<DevopsStageEnvRspDto>> mapStageEnv(Long systemId, Integer deployEnv, List<Integer> deployIds);

    /**
     * 初始化子系统阶段
     *
     * @param subSystemId
     */
    void initDefaultStage(Long subSystemId);
}
