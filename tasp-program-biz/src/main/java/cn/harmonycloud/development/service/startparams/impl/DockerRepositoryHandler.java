package cn.harmonycloud.development.service.startparams.impl;

import cn.harmonycloud.development.outbound.api.dto.pipeline.JenkinsFileStartParameter;
import cn.harmonycloud.development.pojo.dto.devopsstage.DevopsStageEnvDto;
import cn.harmonycloud.development.service.startparams.RunStartParamsContext;
import cn.harmonycloud.development.service.startparams.RunStartParamsEnum;
import cn.harmonycloud.development.service.startparams.RunStartParamsHandler;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/8/7 3:36 下午
 **/
@Component
public class DockerRepositoryHandler implements RunStartParamsHandler {
    @Override
    public RunStartParamsEnum getParams() {
        return RunStartParamsEnum.DOCKER_REPOSITORY;
    }

    @Override
    public void setValue(JenkinsFileStartParameter startParameter, RunStartParamsContext context) {
        DevopsStageEnvDto devopsStageEnvDto = context.getDevopsStageEnvDto();
        if(devopsStageEnvDto.getContainerRepoId() != null){
            startParameter.setValue(devopsStageEnvDto.getContainerRepoId().toString());
            startParameter.setUpdateFlag(Boolean.FALSE);
        }

    }

}
