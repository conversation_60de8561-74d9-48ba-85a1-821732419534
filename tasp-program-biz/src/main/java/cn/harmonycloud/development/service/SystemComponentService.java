package cn.harmonycloud.development.service;

import cn.harmonycloud.development.pojo.entity.SystemComponent;
import cn.harmonycloud.development.outbound.api.dto.scm.GroupDto;

import java.util.List;
import java.util.Map;

public interface SystemComponentService {

    void addGroupId(Long systemId, String name, String desc, String path, Integer groupId);

    String getComponentKeyBySystemId(Long systemId);

    GroupDto getGitlabGroup(Long systemId);

    Integer getCodeGroupId(Long systemId);

    List<SystemComponent> getAllGitlabGroup();

    List<SystemComponent> getAllGitlabGroup(Integer groupId);

    /**
     * 删除所有系统组件
     *
     * @param systemId
     * @return
     */
    void deleteComponent(Long systemId);

    Map<Long, SystemComponent> mapBySystemIds(List<Long> systemIds);

    /**
     * 更新系统后 组件 事件处理
     *
     * @param id
     * @param systemName
     */
    void postUpdateSystem(Long id, String systemName);
}
