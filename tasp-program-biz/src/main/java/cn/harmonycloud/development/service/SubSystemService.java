package cn.harmonycloud.development.service;

import cn.harmonycloud.development.pojo.dto.subsystem.SubsystemCreateRequest;
import cn.harmonycloud.development.pojo.dto.subsystem.SubsystemDetails;
import cn.harmonycloud.development.pojo.dto.subsystem.SubsystemGeneralRequest;
import cn.harmonycloud.development.pojo.dto.subsystem.SubsystemUpdateRequest;
import cn.harmonycloud.development.pojo.entity.DevopsSubSystem;
import cn.harmonycloud.development.pojo.vo.system.*;
import cn.harmonycloud.pmp.model.entity.User;
import cn.harmonycloud.pojo.subsystem.SubsystemConfigDTO;
import cn.harmonycloud.pojo.subsystem.SubsystemConfigSaveRequest;
import cn.harmonycloud.pojo.subsystem.SubsystemDto;
import cn.harmonycloud.pojo.subsystem.SubsystemQueryDto;

import javax.annotation.Nullable;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【hzbank_sub_system(子系统表)】的数据库操作Service
* @createDate 2022-07-28 09:15:15
*/
public interface SubSystemService {

    /**
     * 查旋子系统信息
     *
     * @param subSystemId
     * @return
     */
    SubSystemInfoDto info(Long subSystemId);

    /**
     * 查旋子系统信息（携带代码库id）
     *
     * @param subSystemId
     * @return
     */
    SubSystemInfoDto infoWithGitlab(Long subSystemId);

    /**
     * 根据子系统code查旋子系统信息
     *
     * @param subSystemCode
     * @return
     */
    SubSystemInfoDto infoByCode(String subSystemCode);

    List<SubSystemGitlab> listByGitlabIds(List<String> gitlabIds);


    /**
     * 查询有权限的子系统列表
     * @param systemId
     * @return
     */
    List<Long> listResourceIds(Long systemId);

    /**
     * 系统删除后需要对子系统数据清理
     *
     * @param systemId
     */
    void postRemoveSystem(Long systemId);

    /**
     * 根据参数查询子系统
     *
     * @param queryDto
     * @return
     */
    List<SubsystemDto> list(SubsystemQueryDto queryDto);

    /**
     * 资源相关接口
     *
     * @param request
     * @return
     */
    List<SubsystemDto> listResource(SubsystemGeneralRequest request);

    /**
     * 创建子系统
     *
     * @param request
     * @return
     */
    DevopsSubSystem creatSubsystemPost(DevopsSubSystem subSystem, SubsystemCreateRequest request);

    /**
     * 更新子系统
     *
     * @param request
     * @return
     */
    SubsystemDetails update(SubsystemUpdateRequest request);

    DevopsSubSystem createSubsystem(SubsystemCreateRequest request, User currentUser);

    SubsystemDetails details(Long id, Boolean codeDetails);

    void remove(Long id);

    List<SubsystemDto> list(SubsystemGeneralRequest request);

    /**
     * 查询当前组织下有权限的子系统列表
     *
     * @return
     * @param request
     */
    List<SubsystemDto> resourceListAll(SubsystemGeneralRequest request);

    /**
     *  查询当前用户是否有子系统权限
     *
     * @param subsystemId
     * @param systemId
     * @return 没有权限抛出异常
     */
    void checkPermission(Long subsystemId, @Nullable Long systemId);

    /**
     * 获取应用配置信息
     *
     * @param subsystemId
     * @return
     */
    SubsystemConfigDTO getConfig(Long subsystemId);

    /**
     * 保存子系统配置
     *
     * @param request
     * @return
     */
    SubsystemConfigDTO saveConfig(SubsystemConfigSaveRequest request);
}
