package cn.harmonycloud.development.service.startparams;

import lombok.AllArgsConstructor;

public enum RunStartParamsEnum {


    SUB_CODE("subSystemCode", "子系统编码", Boolean.FALSE, null, Boolean.FALSE),
    STAGE_ENV_CODE("stageEnvCode", "阶段环境编码", Boolean.FALSE, null, Boolean.TRUE),
    SOURCE_BRANCHES("sourceBranches", "源分支", Boolean.TRUE, null, Boolean.FALSE),
    TARGET_BRANCH("targetBranch", "临时分支前缀", Boolean.FALSE, null, Boolean.FALSE),
    DEPLOY_HOST("deployHost", "主机id", null, null, Boolean.FALSE),
    DEPLOY_CLUSTER("deployCluster", "集群id", null, null, Boolean.FALSE),
    DEPLOY_ENV("deployEnv", "部署环境", Boolean.FALSE, null, Boolean.FALSE),
    NAMESPACE("namespace", "集群命名空间", null, null, Boolean.FALSE),
    VERSION("version", "通用制品版本", null, null, Boolean.FALSE, Boolean.TRUE),
    TAG("tag", "容器制品版本", Boolean.FALSE, null, Boolean.FALSE, Boolean.TRUE),
    RAW_REPOSITORY("rawRepository", "通用制品仓库", null, null, Boolean.FALSE),
    DOCKER_REPOSITORY("dockerRepository", "容器制品仓库", null, null, Boolean.FALSE),
    GROUP_ID("groupId", "分组id", Boolean.FALSE, Boolean.FALSE, Boolean.FALSE, Boolean.TRUE);




    private String paramsName;
    private String title;
    private Boolean updateFLag; // 是否可编辑
    private Boolean showFlag; // 是否可见
    private Boolean isDevelopment; // 是否工作台变量
    private Boolean runBuild; // 运行时添加

    RunStartParamsEnum(String paramsName, String title, Boolean updateFLag, Boolean showFlag, Boolean isDevelopment){
        this.paramsName = paramsName;
        this.title = title;
        this.updateFLag = updateFLag;
        this.showFlag = showFlag;
        this.isDevelopment = isDevelopment;
        this.runBuild = Boolean.FALSE;
    }

    RunStartParamsEnum(String paramsName, String title, Boolean updateFLag, Boolean showFlag, Boolean isDevelopment, Boolean runBuild){
        this.paramsName = paramsName;
        this.title = title;
        this.updateFLag = updateFLag;
        this.showFlag = showFlag;
        this.isDevelopment = isDevelopment;
        this.runBuild = runBuild;
    }


    public String getParamsName() {
        return paramsName;
    }

    public Boolean getUpdateFLag() {
        return updateFLag;
    }

    public Boolean getShowFlag() {
        return showFlag;
    }

    public Boolean getDevelopment() {
        return isDevelopment;
    }

    public Boolean getRunBuild(){
        return runBuild;
    }
}
