package cn.harmonycloud.development.service.startparams.impl;

import cn.harmonycloud.constants.DevelopmentConstance;
import cn.harmonycloud.development.outbound.api.dto.pipeline.JenkinsFileStartParameter;
import cn.harmonycloud.development.pojo.dto.devopsstage.DevopsStageEnvDto;
import cn.harmonycloud.development.service.startparams.RunStartParamsContext;
import cn.harmonycloud.development.service.startparams.RunStartParamsEnum;
import cn.harmonycloud.development.service.startparams.RunStartParamsHandler;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/8/7 2:52 下午
 **/
@Component
public class DeployHostHandler implements RunStartParamsHandler {

    @Override
    public RunStartParamsEnum getParams() {
        return RunStartParamsEnum.DEPLOY_HOST;
    }

    @Override
    public void setValue(JenkinsFileStartParameter startParameter, RunStartParamsContext context) {
        DevopsStageEnvDto devopsStageEnvDto = context.getDevopsStageEnvDto();
        if(devopsStageEnvDto.getDeployType() == DevelopmentConstance.EnvDeployType.virtually){
            List<Integer> hostId = devopsStageEnvDto.getHostId();
            List<String> collect = hostId.stream().map(id -> id.toString()).collect(Collectors.toList());
            startParameter.setValue(collect);
            startParameter.setUpdateFlag(Boolean.FALSE);
        }else if (devopsStageEnvDto.getDeployType() == DevelopmentConstance.EnvDeployType.host){
            List<Integer> hostId = devopsStageEnvDto.getHostId();
            List<String> collect = hostId.stream().map(id -> id.toString()).collect(Collectors.toList());
            startParameter.setValue(collect);
            startParameter.setUpdateFlag(Boolean.FALSE);
        }
    }
}
