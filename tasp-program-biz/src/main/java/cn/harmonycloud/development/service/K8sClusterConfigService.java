package cn.harmonycloud.development.service;

import cn.harmonycloud.development.config.K8sClusterConfig;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public interface K8sClusterConfigService {



    /**
     * 获取所有集群配置
     * @return 集群配置列表
     */
     List<K8sClusterConfig.Cluster> getAllClusters();

    /**
     * 根据集群名称获取集群配置
     * @param name 集群名称
     * @return 集群配置，如果未找到返回null
     */
    List<K8sClusterConfig.Cluster> getClusterByName(String name);
}
