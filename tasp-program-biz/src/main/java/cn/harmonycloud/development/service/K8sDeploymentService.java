package cn.harmonycloud.development.service;

import cn.harmonycloud.development.pojo.vo.instance.ServiceDeploymentInfoVO;

import java.util.List;

public interface K8sDeploymentService {







    /**
     * 从Deployment获取服务的部署信息
     *
     * @param masterUrl API Server地址
     * @param token 认证Token
     * @param namespace 命名空间
     * @param serviceName 服务名称
     * @return 服务部署信息
     */
    ServiceDeploymentInfoVO getServiceDeploymentInfoFromDeployment(String clusterName,String masterUrl, String token,
                                                                   String namespace, String serviceName);
}
