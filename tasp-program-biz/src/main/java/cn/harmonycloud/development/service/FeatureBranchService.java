package cn.harmonycloud.development.service;

import cn.harmonycloud.development.pojo.entity.DevopsFeature;
import cn.harmonycloud.development.pojo.entity.FeatureBranch;
import cn.harmonycloud.development.pojo.vo.feature.FeatureBranchQuery;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【feature_branch(特性关联分支表)】的数据库操作Service
* @createDate 2022-08-03 11:35:24
*/
public interface FeatureBranchService {

    /**
     * 创建特性和分支关联关系
     * @param devopsFeature
     */
    FeatureBranch create(DevopsFeature devopsFeature, String sourceBranch,Long issueId);

    /**
     * 校验是否可以创建分支
     *
     * @param subSystemId
     * @param sourceBranch
     */
    void createBranchCheck(long subSystemId, String sourceBranch);

    /**
     * 获取所有分支
     *
     * @param records
     * @return
     */
    List<String> listAllBranch(List<FeatureBranch> records, boolean withClear, boolean withDevBranch);

}
