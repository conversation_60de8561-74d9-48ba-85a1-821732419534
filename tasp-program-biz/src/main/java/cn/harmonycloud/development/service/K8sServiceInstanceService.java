package cn.harmonycloud.development.service;

import cn.harmonycloud.development.pojo.vo.instance.K8sServiceInstanceVO;

import java.util.List;

public interface K8sServiceInstanceService {


    /**
     * 根据集群信息直接获取服务实例（不依赖数据库中的clusterId）
     *
     * @param clusterName 集群名称
     * @param masterUrl   API Server地址
     * @param token       认证Token
     * @param namespace   命名空间
     * @param serviceName 服务名称
     * @return 服务实例列表
     */
    List<K8sServiceInstanceVO> getServiceInstancesDirect(String clusterName, String masterUrl, String token,
                                                         String namespace, String serviceName);


    boolean validateConnection(String masterUrl, String token);





}
