package cn.harmonycloud.development.service.startparams.impl;

import cn.harmonycloud.development.outbound.DevopsStageEnvFeatureRepository;
import cn.harmonycloud.development.outbound.FeatureBranchRepository;
import cn.harmonycloud.development.outbound.SystemDictRepository;
import cn.harmonycloud.development.outbound.api.dto.pipeline.JenkinsFileStartParameter;
import cn.harmonycloud.development.pojo.dto.devopsstage.DevopsStageEnvDto;
import cn.harmonycloud.development.pojo.entity.FeatureBranch;
import cn.harmonycloud.development.pojo.entity.SystemDict;
import cn.harmonycloud.development.service.startparams.RunStartParamsContext;
import cn.harmonycloud.development.service.startparams.RunStartParamsEnum;
import cn.harmonycloud.development.service.startparams.RunStartParamsHandler;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description 代码合并源分支
 * <AUTHOR>
 * @Date 2023/8/7 9:09 上午
 **/
@Component
public class SourceBranchesHandler implements RunStartParamsHandler {

    @Autowired
    private DevopsStageEnvFeatureRepository stageEnvFeatureRepository;
    @Autowired
    private FeatureBranchRepository featureBranchRepository;
    @Autowired
    private SystemDictRepository systemDictRepository;

    @Override
    public RunStartParamsEnum getParams() {
        return RunStartParamsEnum.SOURCE_BRANCHES;
    }

    @Override
    public void setValue(JenkinsFileStartParameter startParameter, RunStartParamsContext context) {
        DevopsStageEnvDto devopsStageEnvDto = context.getDevopsStageEnvDto();
        SystemDict systemDict = systemDictRepository.getCacheById(devopsStageEnvDto.getStageDictId());
        Long majorVersionId = context.getVersionManagement() == null ? null : context.getVersionManagement().getMajorVersionId();
        List<Long> featureIds = stageEnvFeatureRepository.selectFeatureList(devopsStageEnvDto.getId(), majorVersionId);
        if(CollectionUtils.isEmpty(featureIds)){
            startParameter.setValue(new ArrayList<>());
            return ;
        }
        List<FeatureBranch> featureBranches = featureBranchRepository.listByParam(featureIds).stream().filter(fb -> !fb.getIsClear()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(featureBranches)) {
            startParameter.setValue(new ArrayList<>());
            return;
        }
        List<String> sourceBranches = featureBranches.stream().map(fb -> fb.getBranch(Integer.parseInt(systemDict.getDictType()))).collect(Collectors.toList());
        startParameter.setValue(sourceBranches);
    }

}
