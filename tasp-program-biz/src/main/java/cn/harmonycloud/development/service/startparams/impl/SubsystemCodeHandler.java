package cn.harmonycloud.development.service.startparams.impl;

import cn.harmonycloud.development.outbound.api.dto.pipeline.JenkinsFileStartParameter;
import cn.harmonycloud.development.pojo.entity.DevopsSubSystem;
import cn.harmonycloud.development.service.startparams.RunStartParamsEnum;
import cn.harmonycloud.development.service.startparams.RunStartParamsContext;
import cn.harmonycloud.development.service.startparams.RunStartParamsHandler;
import org.springframework.stereotype.Component;

/**
 * @Description 子系统编码
 * <AUTHOR>
 * @Date 2023/8/4 3:50 下午
 **/
@Component
public class SubsystemCodeHandler implements RunStartParamsHandler {


    @Override
    public RunStartParamsEnum getParams() {
        return RunStartParamsEnum.SUB_CODE;
    }


    @Override
    public void setValue(JenkinsFileStartParameter startParameter, RunStartParamsContext context) {
        DevopsSubSystem devopsSubSystem = context.getDevopsSubSystem();
        startParameter.setValue(devopsSubSystem.getSubCode());
    }


}
