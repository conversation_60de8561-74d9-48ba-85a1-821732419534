package cn.harmonycloud.development.outbound.db.mapper;

import cn.harmonycloud.development.pojo.entity.VersionManagement;
import cn.harmonycloud.development.pojo.entity.VersionSubsystem;
import cn.harmonycloud.development.pojo.vo.version.VersionSubsystemQuery;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description:
 * @author: yx
 * @time: 2022/8/22
 */
@Mapper
public interface VersionMapper extends BaseMapper<VersionManagement> {


}
